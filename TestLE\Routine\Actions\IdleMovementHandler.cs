using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles idle movement when player is stationary for too long.
/// Medium priority action to prevent getting stuck and maintain activity.
/// Follows Single Responsibility Principle - only handles idle movement logic.
/// </summary>
public class IdleMovementHandler : IGameAction
{
    public int Priority => 400; // Medium priority - maintenance action
    public string Name => "Idle Movement Handler";

    private float _idleTime = 0f;

    /// <summary>
    /// Checks if player has been idle long enough to trigger movement.
    /// Uses configured thresholds instead of hard-coded values.
    /// </summary>
    /// <returns>True if idle movement should be triggered</returns>
    public bool CanExecute()
    {
        try
        {
            if (PLAYER?.movingState?.myAgent == null)
                return false;

            // Check if player is moving
            var velocity = PLAYER.movingState.myAgent.velocity.magnitude;
            
            if (velocity <= GameConfiguration.Movement.IdleVelocityThreshold)
            {
                // Player is idle - accumulate idle time
                _idleTime += Time.deltaTime;
                return _idleTime >= GameConfiguration.Movement.IdleTimeThreshold;
            }
            else
            {
                // Player is moving - reset idle time
                _idleTime = 0f;
                return false;
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking idle movement conditions: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes idle movement to a random nearby position.
    /// Attempts navigation mesh first, falls back to direct movement.
    /// </summary>
    /// <returns>Coroutine for idle movement</returns>
    public IEnumerator Execute()
    {
        MelonLogger.Msg("Player idle detected - initiating random movement");

        try
        {
            // Reset idle time
            _idleTime = 0f;

            // Attempt movement
            yield return PerformIdleMovement();

            // Wait after movement
            yield return new WaitForSeconds(GameConfiguration.Movement.IdleMovementWaitTime);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during idle movement: {ex.Message}");
            _idleTime = 0f; // Reset to prevent spam
        }
    }

    /// <summary>
    /// Performs the actual idle movement with fallback options.
    /// Tries navigation mesh first, then direct random movement.
    /// </summary>
    /// <returns>Coroutine for movement execution</returns>
    private static IEnumerator PerformIdleMovement()
    {
        var playerPosition = PLAYER.transform.position;
        Vector3 targetPosition;

        // Try to find a valid position on the navigation mesh
        if (UnityHelpers.RandomPointOnNavMesh(
            playerPosition, 
            GameConfiguration.Movement.IdleMovementRange, 
            out var navMeshPosition))
        {
            targetPosition = navMeshPosition;
            MelonLogger.Msg($"Moving to navigation mesh position: {targetPosition}");
        }
        else
        {
            // Fallback to random position (may not be on navmesh)
            targetPosition = playerPosition + new Vector3(
                Random.Range(-GameConfiguration.Movement.FallbackMovementRange, GameConfiguration.Movement.FallbackMovementRange),
                0,
                Random.Range(-GameConfiguration.Movement.FallbackMovementRange, GameConfiguration.Movement.FallbackMovementRange)
            );
            MelonLogger.Msg($"Navigation mesh failed - moving to fallback position: {targetPosition}");
        }

        // Execute movement
        PlayerHelpers.MoveTo(targetPosition);
        
        yield break;
    }

    /// <summary>
    /// Gets current idle time for debugging and monitoring.
    /// </summary>
    /// <returns>Current idle time in seconds</returns>
    public float GetCurrentIdleTime()
    {
        return _idleTime;
    }

    /// <summary>
    /// Gets time remaining until idle movement triggers.
    /// </summary>
    /// <returns>Seconds until idle movement, or 0 if ready now</returns>
    public float GetTimeUntilIdleMovement()
    {
        return Math.Max(0f, GameConfiguration.Movement.IdleTimeThreshold - _idleTime);
    }

    /// <summary>
    /// Resets the idle timer.
    /// Useful when manual movement occurs or for testing.
    /// </summary>
    public void ResetIdleTimer()
    {
        _idleTime = 0f;
        MelonLogger.Msg("Idle movement timer reset");
    }

    /// <summary>
    /// Forces immediate idle movement regardless of timer.
    /// Useful for unsticking player or testing movement.
    /// </summary>
    /// <returns>Coroutine for forced movement</returns>
    public IEnumerator ForceIdleMovement()
    {
        MelonLogger.Msg("Forcing immediate idle movement");
        _idleTime = 0f;
        yield return PerformIdleMovement();
        yield return new WaitForSeconds(GameConfiguration.Movement.IdleMovementWaitTime);
    }

    /// <summary>
    /// Gets current movement status for debugging.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public string GetMovementStatus()
    {
        if (PLAYER?.movingState?.myAgent == null)
            return "Player agent unavailable";

        var velocity = PLAYER.movingState.myAgent.velocity.magnitude;
        var isIdle = velocity <= GameConfiguration.Movement.IdleVelocityThreshold;
        
        return $"Velocity: {velocity:F2}, Idle: {isIdle}, Idle Time: {_idleTime:F1}s";
    }
}
