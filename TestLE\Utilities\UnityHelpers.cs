﻿using MelonLoader;
using UnityEngine;
using UnityEngine.AI;
using Random = UnityEngine.Random;

namespace TestLE.Utilities;

public static class UnityHelpers
{
    // public static Transform? FindDeepChild(this Transform parent, string name)
    // {
    //     foreach (var o in parent)
    //     {
    //         var child = o.TryCast<Transform>();
    //         if (child == null)
    //             continue;
    //         
    //         if (child.name == name)
    //             return child;
    //         
    //         var result = child.FindDeepChild(name);
    //         if (result != null)
    //             return result;
    //     }
    //     
    //     return null;
    // }

    public static Transform? FindDeepChild(this Transform parent, string name)
    {
        var names = name.Split('/');
        return FindDeepChildRecursive(parent, names, 0);
    }

    private static Transform? FindDeepChildRecursive(Transform parent, string[] names, int index)
    {
        if (parent == null || index < 0 || index >= names.Length)
            return null;

        foreach (var o in parent)
        {
            var child = o.TryCast<Transform>();
            // MelonLogger.Msg(child != null ? $"FindDeepChildRecursive {child.name}" : "FindDeepChildRecursive null");
            if (child != null && child.name == names[index])
                return index == names.Length - 1 ? child : FindDeepChildRecursive(child, names, index + 1);
        }

        return null;
    }

    public static List<Transform> FindChildren(this Transform parent, string name)
    {
        var children = new List<Transform>();
        foreach (var o in parent)
        {
            var child = o.TryCast<Transform>();
            if (child != null && child.name == name)
                children.Add(child);
        }

        return children;
    }

    public static List<GameObject> FindChildren(this GameObject parent, string name)
    {
        var children = new List<GameObject>();
        foreach (var o in parent.transform)
        {
            var child = o.TryCast<Transform>();
            if (child != null && child.name == name)
                children.Add(child.gameObject);
        }

        return children;
    }

    public static bool RandomPointOnNavMesh(Vector3 center, float range, out Vector3 result, int maxTries = 30, float maxDeviation = 1.0f)
    {
        for (var i = 0; i < maxTries; i++)
        {
            var randomPoint = center + Random.insideUnitSphere * range;
            if (NavMesh.SamplePosition(randomPoint, out var hit, maxDeviation, -1))
            {
                result = hit.position;
                return true;
            }
        }

        result = Vector3.zero;
        return false;
    }

    public static bool RandomPointOnEdgeOfCircleOnNavMesh(Vector3 center, float range, out Vector3 result, int maxTries = 30, float maxDeviation = 1.0f)
    {
        for (var i = 0; i < maxTries; i++)
        {
            var randomEdge = Random.insideUnitCircle * range;
            var randomPoint = center + new Vector3(randomEdge.x, 0, randomEdge.y);
            if (NavMesh.SamplePosition(randomPoint, out var hit, maxDeviation, -1))
            {
                result = hit.position;
                return true;
            }
        }

        result = Vector3.zero;
        return false;
    }
}
