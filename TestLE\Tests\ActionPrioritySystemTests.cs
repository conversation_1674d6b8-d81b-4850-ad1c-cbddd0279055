using System.Collections;
using TestLE.Routines.Core;

namespace TestLE.Tests;

/// <summary>
/// Example unit tests for the ActionPrioritySystem.
/// Demonstrates how the new modular architecture enables proper testing.
/// 
/// In a real project, you would use a testing framework like NUnit or xUnit.
/// These are simplified examples showing the testable design.
/// </summary>
public static class ActionPrioritySystemTests
{
    /// <summary>
    /// Test that actions are executed in priority order.
    /// </summary>
    public static void TestActionPriorityOrdering()
    {
        // Arrange
        var mockValidator = new MockGameStateValidator(true);
        var actions = new List<IGameAction>
        {
            new MockAction("Low Priority", 100, true),
            new MockAction("High Priority", 1000, true),
            new MockAction("Medium Priority", 500, true)
        };

        var system = new ActionPrioritySystem(actions, mockValidator);

        // Act
        var nextAction = system.ExecuteNextAction();

        // Assert
        // Should execute the highest priority action (1000)
        // In a real test framework, you would use Assert.AreEqual or similar
        Console.WriteLine($"Expected: High Priority, Actual: {GetActionName(nextAction)}");
    }

    /// <summary>
    /// Test that system handles no executable actions gracefully.
    /// </summary>
    public static void TestNoExecutableActions()
    {
        // Arrange
        var mockValidator = new MockGameStateValidator(true);
        var actions = new List<IGameAction>
        {
            new MockAction("Action 1", 100, false), // Cannot execute
            new MockAction("Action 2", 200, false)  // Cannot execute
        };

        var system = new ActionPrioritySystem(actions, mockValidator);

        // Act
        var nextAction = system.ExecuteNextAction();

        // Assert
        // Should return null when no actions can execute
        Console.WriteLine($"Expected: null, Actual: {(nextAction == null ? "null" : "not null")}");
    }

    /// <summary>
    /// Test that system handles invalid game state gracefully.
    /// </summary>
    public static void TestInvalidGameState()
    {
        // Arrange
        var mockValidator = new MockGameStateValidator(false); // Invalid state
        var actions = new List<IGameAction>
        {
            new MockAction("Action", 100, true)
        };

        var system = new ActionPrioritySystem(actions, mockValidator);

        // Act
        var nextAction = system.ExecuteNextAction();

        // Assert
        // Should return null when game state is invalid
        Console.WriteLine($"Expected: null, Actual: {(nextAction == null ? "null" : "not null")}");
    }

    /// <summary>
    /// Runs all tests.
    /// </summary>
    public static void RunAllTests()
    {
        Console.WriteLine("Running ActionPrioritySystem Tests...");
        
        TestActionPriorityOrdering();
        TestNoExecutableActions();
        TestInvalidGameState();
        
        Console.WriteLine("Tests completed.");
    }

    // Helper method to extract action name from coroutine
    private static string GetActionName(IEnumerator? coroutine)
    {
        // In a real implementation, you would have a way to identify the action
        // This is a simplified example
        return coroutine?.ToString() ?? "null";
    }
}

/// <summary>
/// Mock implementation of IGameAction for testing.
/// </summary>
public class MockAction : IGameAction
{
    public int Priority { get; }
    public string Name { get; }
    private readonly bool _canExecute;

    public MockAction(string name, int priority, bool canExecute)
    {
        Name = name;
        Priority = priority;
        _canExecute = canExecute;
    }

    public bool CanExecute() => _canExecute;

    public IEnumerator Execute()
    {
        Console.WriteLine($"Executing {Name}");
        yield break;
    }
}

/// <summary>
/// Mock implementation of IGameStateValidator for testing.
/// </summary>
public class MockGameStateValidator : IGameStateValidator
{
    private readonly bool _isValid;

    public MockGameStateValidator(bool isValid)
    {
        _isValid = isValid;
    }

    public GameStateValidationResult ValidateGameState()
    {
        return _isValid 
            ? GameStateValidationResult.Success() 
            : GameStateValidationResult.Failure("Mock validation failure");
    }

    public bool IsMainRoutineValid() => _isValid;
    public bool IsPlayerValid() => _isValid;
    public bool IsCombatRoutineValid() => _isValid;
}
