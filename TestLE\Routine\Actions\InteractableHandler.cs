using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles interaction with world objects like shrines and clickable items.
/// Medium priority action for interacting with beneficial objects.
/// Follows Single Responsibility Principle - only handles interactable logic.
/// </summary>
public class InteractableHandler : IGameAction
{
    public int Priority => 300; // Medium priority - beneficial but not urgent
    public string Name => "Interactable Handler";

    /// <summary>
    /// Checks if there are interactable objects within range.
    /// Prioritizes good shrines over general interactables.
    /// </summary>
    /// <returns>True if interaction should be performed</returns>
    public bool CanExecute()
    {
        try
        {
            // Check for good shrines first (higher priority)
            if (GOOD_SHRINES.Count > 0)
                return true;

            // Check for general interactables within range
            return HasInteractablesInRange();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking interactable conditions: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes interaction with the most appropriate object.
    /// Prioritizes good shrines over general interactables.
    /// </summary>
    /// <returns>Coroutine for interaction</returns>
    public IEnumerator Execute()
    {
        try
        {
            // Handle good shrines first (they have higher value)
            if (GOOD_SHRINES.Count > 0)
            {
                yield return HandleGoodShrines();
            }
            else
            {
                // Handle general interactables
                yield return HandleNearestInteractable();
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during interaction: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles interaction with good shrines.
    /// Cleans up null entries and interacts with the nearest shrine.
    /// </summary>
    /// <returns>Coroutine for shrine interaction</returns>
    private static IEnumerator HandleGoodShrines()
    {
        MelonLogger.Msg("Handling good shrines");

        // Clean up null shrines
        CleanupNullShrines();

        if (GOOD_SHRINES.Count == 0)
            yield break;

        // Sort shrines by distance (nearest first)
        var playerPos = PLAYER.transform.position;
        GOOD_SHRINES.Sort((s1, s2) => 
        {
            if (s1 == null && s2 == null) return 0;
            if (s1 == null) return 1;
            if (s2 == null) return -1;
            
            var dist1 = Vector3.Distance(playerPos, s1.transform.position);
            var dist2 = Vector3.Distance(playerPos, s2.transform.position);
            return dist1.CompareTo(dist2);
        });

        var shrine = GOOD_SHRINES.First();
        if (shrine == null)
        {
            GOOD_SHRINES.RemoveAt(0);
            yield break;
        }

        // Move to shrine
        PlayerHelpers.MoveTo(shrine.transform.position);
        yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);

        // Check if in interaction range
        var distance = Vector3.Distance(PLAYER.transform.position, shrine.transform.position);
        if (distance <= shrine.interactionRange)
        {
            MelonLogger.Msg($"Interacting with shrine at distance {distance:F1}");
            shrine.ObjectClick(PLAYER.gameObject, true);
            GOOD_SHRINES.Remove(shrine);
        }
        else
        {
            MelonLogger.Msg($"Shrine too far ({distance:F1} > {shrine.interactionRange}) - will retry");
        }
    }

    /// <summary>
    /// Handles interaction with general interactable objects.
    /// Finds nearest interactable within range and interacts with it.
    /// </summary>
    /// <returns>Coroutine for interactable interaction</returns>
    private static IEnumerator HandleNearestInteractable()
    {
        var interactable = FindNearestInteractable();
        if (interactable == null)
            yield break;

        MelonLogger.Msg("Interacting with nearest interactable object");

        try
        {
            // Move to interactable with appropriate range
            var targetRange = interactable.interactionRange * 0.8f; // Slight buffer
            yield return PlayerHelpers.MoveToForce(interactable.transform.position, targetRange);

            // Remove from list and interact
            INTERACTABLES.Remove(interactable);
            interactable.ObjectClick(PLAYER.gameObject, true);

            MelonLogger.Msg("Interactable interaction completed");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error interacting with object: {ex.Message}");
            // Remove problematic interactable to prevent infinite loops
            INTERACTABLES.Remove(interactable);
        }
    }

    /// <summary>
    /// Finds the nearest interactable object within range.
    /// Cleans up invalid entries during the search.
    /// </summary>
    /// <returns>Nearest valid interactable or null</returns>
    private static WorldObjectClickListener? FindNearestInteractable()
    {
        if (PLAYER == null)
            return null;

        WorldObjectClickListener? nearestInteractable = null;
        float nearestDistance = float.MaxValue;

        // Clean up and find nearest interactable
        for (int i = INTERACTABLES.Count - 1; i >= 0; i--)
        {
            var interactable = INTERACTABLES[i];
            
            // Remove null or inactive interactables
            if (interactable == null || !interactable.isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
                continue;
            }

            // Check distance
            var distance = Vector3.Distance(PLAYER.transform.position, interactable.transform.position);
            
            // Skip if too far
            if (distance > GameConfiguration.Combat.InteractableRange)
                continue;

            // Update nearest
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestInteractable = interactable;
            }
        }

        return nearestInteractable;
    }

    /// <summary>
    /// Checks if there are any interactables within interaction range.
    /// </summary>
    /// <returns>True if interactables are available</returns>
    private static bool HasInteractablesInRange()
    {
        return FindNearestInteractable() != null;
    }

    /// <summary>
    /// Cleans up null shrine entries from the list.
    /// </summary>
    private static void CleanupNullShrines()
    {
        for (int i = GOOD_SHRINES.Count - 1; i >= 0; i--)
        {
            if (GOOD_SHRINES[i] == null)
            {
                GOOD_SHRINES.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// Gets current interactable status for debugging.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public static string GetInteractableStatus()
    {
        var nearestInteractable = FindNearestInteractable();
        var nearestDistance = nearestInteractable != null 
            ? Vector3.Distance(PLAYER.transform.position, nearestInteractable.transform.position)
            : float.MaxValue;

        return $"Good Shrines: {GOOD_SHRINES.Count}, " +
               $"Interactables: {INTERACTABLES.Count}, " +
               $"Nearest: {(nearestInteractable != null ? $"{nearestDistance:F1}m" : "None")}";
    }

    /// <summary>
    /// Forces cleanup of all interactable lists.
    /// Useful for area transitions or debugging.
    /// </summary>
    public static void CleanupAllInteractables()
    {
        CleanupNullShrines();
        
        for (int i = INTERACTABLES.Count - 1; i >= 0; i--)
        {
            if (INTERACTABLES[i] == null || !INTERACTABLES[i].isActiveAndEnabled)
            {
                INTERACTABLES.RemoveAt(i);
            }
        }

        MelonLogger.Msg("All interactable lists cleaned up");
    }
}
