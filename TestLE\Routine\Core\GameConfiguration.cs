namespace TestLE.Routines.Core;

/// <summary>
/// Configuration settings for game behavior.
/// Replaces hard-coded magic numbers with configurable, documented values.
/// Follows the principle of making implicit dependencies explicit.
/// </summary>
public static class GameConfiguration
{
    /// <summary>
    /// Movement and positioning settings
    /// </summary>
    public static class Movement
    {
        /// <summary>
        /// Velocity threshold below which player is considered idle (units/second)
        /// </summary>
        public const float IdleVelocityThreshold = 0.1f;

        /// <summary>
        /// Time in seconds before triggering idle movement
        /// </summary>
        public const float IdleTimeThreshold = 5f;

        /// <summary>
        /// Maximum distance for random idle movement
        /// </summary>
        public const float IdleMovementRange = 10f;

        /// <summary>
        /// Fallback movement range when navigation mesh fails
        /// </summary>
        public const float FallbackMovementRange = 50f;

        /// <summary>
        /// Wait time after idle movement (seconds)
        /// </summary>
        public const float IdleMovementWaitTime = 2f;
    }

    /// <summary>
    /// Combat and enemy detection settings
    /// </summary>
    public static class Combat
    {
        /// <summary>
        /// Maximum distance to search for enemies
        /// </summary>
        public const float EnemySearchRange = 100f;

        /// <summary>
        /// Distance threshold for close combat
        /// </summary>
        public const float CloseCombatRange = 3f;

        /// <summary>
        /// Maximum distance for interactable objects
        /// </summary>
        public const float InteractableRange = 20f;

        /// <summary>
        /// Random movement range when no enemies found
        /// </summary>
        public const float NoEnemyMovementRange = 5f;

        /// <summary>
        /// Wait time when no enemies found (seconds)
        /// </summary>
        public const float NoEnemyWaitTime = 1f;

        /// <summary>
        /// Maximum inactive enemy attempts before removal
        /// </summary>
        public const int MaxInactiveEnemyAttempts = 10;
    }

    /// <summary>
    /// Timing and intervals
    /// </summary>
    public static class Timing
    {
        /// <summary>
        /// Interval for automatic material storage (seconds)
        /// </summary>
        public const float MaterialStorageInterval = 10f;

        /// <summary>
        /// Standard coroutine wait time (seconds)
        /// </summary>
        public const float StandardWaitTime = 0.3333f;

        /// <summary>
        /// Short wait time for quick operations (seconds)
        /// </summary>
        public const float ShortWaitTime = 0.1f;

        /// <summary>
        /// Wait time after ground item drops (seconds)
        /// </summary>
        public const float GroundItemWaitTime = 1f;
    }

    /// <summary>
    /// Stash and inventory management
    /// </summary>
    public static class Stash
    {
        /// <summary>
        /// Starting stash tab index
        /// </summary>
        public const int DefaultStashTab = 0;

        /// <summary>
        /// Wait time between stash operations (seconds)
        /// </summary>
        public const float StashOperationWaitTime = 0.1f;
    }
}
