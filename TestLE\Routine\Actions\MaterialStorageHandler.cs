using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles automatic material storage at regular intervals.
/// High priority maintenance action to prevent inventory overflow.
/// Follows Single Responsibility Principle - only handles material storage.
/// </summary>
public class MaterialStorageHandler : IGameAction
{
    public int Priority => 900; // High priority - prevents inventory issues
    public string Name => "Material Storage Handler";

    private DateTime _lastStorageTime = DateTime.MinValue;

    /// <summary>
    /// Checks if it's time to store materials based on configured interval.
    /// Uses configuration instead of hard-coded values.
    /// </summary>
    /// <returns>True if materials should be stored now</returns>
    public bool CanExecute()
    {
        try
        {
            // First run or enough time has passed
            return _lastStorageTime == DateTime.MinValue || 
                   (DateTime.Now - _lastStorageTime).TotalSeconds >= GameConfiguration.Timing.MaterialStorageInterval;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking material storage timing: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes material storage and updates last storage time.
    /// Handles errors gracefully to prevent blocking other actions.
    /// </summary>
    /// <returns>Coroutine for material storage</returns>
    public IEnumerator Execute()
    {
        try
        {
            MelonLogger.Msg("Storing materials automatically");
            
            // Store materials using existing helper
            PlayerHelpers.StoreMaterials();
            
            // Update last storage time
            _lastStorageTime = DateTime.Now;
            
            MelonLogger.Msg("Materials stored successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error storing materials: {ex.Message}");
            // Still update time to prevent spam attempts
            _lastStorageTime = DateTime.Now;
        }

        // No yield needed - storage is synchronous
        yield break;
    }

    /// <summary>
    /// Resets the storage timer.
    /// Useful for testing or when manual storage occurs.
    /// </summary>
    public void ResetTimer()
    {
        _lastStorageTime = DateTime.MinValue;
        MelonLogger.Msg("Material storage timer reset");
    }

    /// <summary>
    /// Forces immediate material storage regardless of timer.
    /// Useful for critical situations like monolith completion.
    /// </summary>
    /// <returns>Coroutine for forced storage</returns>
    public IEnumerator ForceStorage()
    {
        MelonLogger.Msg("Forcing immediate material storage");
        yield return Execute();
    }

    /// <summary>
    /// Gets time until next scheduled storage.
    /// Useful for debugging and monitoring.
    /// </summary>
    /// <returns>Seconds until next storage, or 0 if ready now</returns>
    public double GetTimeUntilNextStorage()
    {
        if (_lastStorageTime == DateTime.MinValue)
            return 0;

        var elapsed = (DateTime.Now - _lastStorageTime).TotalSeconds;
        var remaining = GameConfiguration.Timing.MaterialStorageInterval - elapsed;
        return Math.Max(0, remaining);
    }
}
