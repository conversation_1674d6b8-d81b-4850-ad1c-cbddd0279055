namespace TestLE.Routines.Core;

/// <summary>
/// Validates critical game state before action execution.
/// Centralizes null checks and state validation following DRY principle.
/// </summary>
public interface IGameStateValidator
{
    /// <summary>
    /// Validates that all critical game objects are available and valid.
    /// </summary>
    /// <returns>Validation result with success status and error message if failed</returns>
    GameStateValidationResult ValidateGameState();

    /// <summary>
    /// Validates that the main routine is properly initialized and active.
    /// </summary>
    /// <returns>True if routine is valid, false otherwise</returns>
    bool IsMainRoutineValid();

    /// <summary>
    /// Validates that the player object is available and in a valid state.
    /// </summary>
    /// <returns>True if player is valid, false otherwise</returns>
    bool IsPlayerValid();

    /// <summary>
    /// Validates that the current combat routine is available.
    /// </summary>
    /// <returns>True if combat routine is valid, false otherwise</returns>
    bool IsCombatRoutineValid();
}

/// <summary>
/// Result of game state validation with detailed error information.
/// </summary>
public readonly struct GameStateValidationResult
{
    public bool IsValid { get; }
    public string ErrorMessage { get; }

    public GameStateValidationResult(bool isValid, string errorMessage = "")
    {
        IsValid = isValid;
        ErrorMessage = errorMessage;
    }

    public static GameStateValidationResult Success() => new(true);
    public static GameStateValidationResult Failure(string error) => new(false, error);
}
