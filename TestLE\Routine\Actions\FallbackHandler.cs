using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;
using Random = UnityEngine.Random;

namespace TestLE.Routines.Actions;

/// <summary>
/// Fallback handler for when no other actions can execute.
/// Lowest priority action that always executes to prevent the bot from getting stuck.
/// Follows Single Responsibility Principle - only handles fallback scenarios.
/// </summary>
public class FallbackHandler : IGameAction
{
    public int Priority => 100; // Lowest priority - only executes when nothing else can
    public string Name => "Fallback Handler";

    /// <summary>
    /// Always returns true as this is the fallback action.
    /// Ensures the bot never gets completely stuck without any action.
    /// </summary>
    /// <returns>Always true</returns>
    public bool CanExecute()
    {
        return true; // Fallback always executes if reached
    }

    /// <summary>
    /// Executes fallback behavior based on current game state.
    /// Attempts to find enemies or move to maintain activity.
    /// </summary>
    /// <returns>Coroutine for fallback action</returns>
    public IEnumerator Execute()
    {
        MelonLogger.Msg("Executing fallback action - no other actions available");

        try
        {
            // Try to find enemies first
            var (enemy, distance) = FindHelpers.FindNearestEnemy(
                PLAYER.transform.position, 
                GameConfiguration.Combat.EnemySearchRange);

            if (enemy != null)
            {
                // Enemy found but other actions couldn't handle it - try combat anyway
                yield return FallbackCombat(enemy, distance);
            }
            else
            {
                // No enemies - perform exploration movement
                yield return FallbackExploration();
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in fallback handler: {ex.Message}");
            // Even fallback failed - just wait and hope for recovery
            yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
        }
    }

    /// <summary>
    /// Fallback combat when normal combat handler couldn't execute.
    /// Attempts basic combat routine or movement toward enemy.
    /// </summary>
    /// <param name="enemy">Target enemy</param>
    /// <param name="distance">Distance to enemy</param>
    /// <returns>Coroutine for fallback combat</returns>
    private static IEnumerator FallbackCombat(Enemy enemy, float distance)
    {
        MelonLogger.Msg($"Fallback combat with enemy at distance {distance:F1}");

        try
        {
            if (CURRENT_ROUTINE != null && enemy.Data?.gameObject.active == true)
            {
                // Try to use combat routine
                yield return CURRENT_ROUTINE.Run(enemy, enemy.Data.transform, distance);
            }
            else
            {
                // Combat routine unavailable or enemy inactive - just move toward enemy
                if (enemy.Data?.transform != null)
                {
                    PlayerHelpers.MoveTo(enemy.Data.transform.position);
                    yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
                }
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in fallback combat: {ex.Message}");
            yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
        }
    }

    /// <summary>
    /// Fallback exploration when no enemies are found.
    /// Performs random movement to explore and potentially find enemies.
    /// </summary>
    /// <returns>Coroutine for fallback exploration</returns>
    private static IEnumerator FallbackExploration()
    {
        MelonLogger.Msg("No enemies found - performing fallback exploration");

        try
        {
            // Generate random movement position
            var currentPosition = PLAYER.transform.position;
            var randomOffset = new Vector3(
                Random.Range(-GameConfiguration.Combat.NoEnemyMovementRange, GameConfiguration.Combat.NoEnemyMovementRange),
                0,
                Random.Range(-GameConfiguration.Combat.NoEnemyMovementRange, GameConfiguration.Combat.NoEnemyMovementRange)
            );

            var targetPosition = currentPosition + randomOffset;

            // Move to random position
            PlayerHelpers.MoveTo(targetPosition);
            yield return new WaitForSeconds(GameConfiguration.Combat.NoEnemyWaitTime);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error in fallback exploration: {ex.Message}");
            yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
        }
    }

    /// <summary>
    /// Gets fallback status for debugging.
    /// Provides information about why fallback was triggered.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public static string GetFallbackStatus()
    {
        var (enemy, distance) = FindHelpers.FindNearestEnemy(
            PLAYER.transform.position, 
            GameConfiguration.Combat.EnemySearchRange);

        var enemyStatus = enemy != null ? $"Enemy at {distance:F1}m" : "No enemies";
        var routineStatus = CURRENT_ROUTINE != null ? "Routine OK" : "No routine";

        return $"Fallback Status - {enemyStatus}, {routineStatus}";
    }

    /// <summary>
    /// Performs emergency recovery actions.
    /// Used when the bot appears to be completely stuck.
    /// </summary>
    /// <returns>Coroutine for emergency recovery</returns>
    public static IEnumerator EmergencyRecovery()
    {
        MelonLogger.Warning("Performing emergency recovery");

        try
        {
            // Clear potentially problematic state
            ENEMIES.Clear();
            GROUND_ITEMS.Clear();
            INTERACTABLES.Clear();
            MONOLITH_OBJECTIVES.Clear();
            GOOD_SHRINES.Clear();

            // Perform large random movement
            var currentPosition = PLAYER.transform.position;
            var largeRandomOffset = new Vector3(
                Random.Range(-50f, 50f),
                0,
                Random.Range(-50f, 50f)
            );

            PlayerHelpers.MoveTo(currentPosition + largeRandomOffset);
            yield return new WaitForSeconds(2f);

            MelonLogger.Msg("Emergency recovery completed");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during emergency recovery: {ex.Message}");
        }
    }
}
