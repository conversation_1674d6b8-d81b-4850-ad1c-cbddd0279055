using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles player death and respawn logic.
/// Highest priority action as death must be handled immediately.
/// Follows Single Responsibility Principle - only handles death scenarios.
/// </summary>
public class DeathHandler : IGameAction
{
    public int Priority => 1000; // Highest priority - death must be handled immediately
    public string Name => "Death Handler";

    /// <summary>
    /// Checks if player is currently dead (death screen is active).
    /// Fast check with no side effects.
    /// </summary>
    /// <returns>True if death screen is active and enabled</returns>
    public bool CanExecute()
    {
        try
        {
            var deathScreen = FindHelpers.FindDeathScreen();
            return deathScreen != null && deathScreen.isActiveAndEnabled;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking death screen: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes death and respawn sequence.
    /// Handles respawn and navigation to next monolith.
    /// </summary>
    /// <returns>Coroutine for death handling</returns>
    public IEnumerator Execute()
    {
        MelonLogger.Msg("Player death detected - initiating respawn sequence");

        var deathScreen = FindHelpers.FindDeathScreen();
        if (deathScreen == null)
        {
            MelonLogger.Error("Death screen not found during execution");
            yield break;
        }

        yield return HandleDeathAndRespawn(deathScreen);
    }

    /// <summary>
    /// Handles the complete death and respawn process.
    /// Separated for clarity and potential reuse.
    /// </summary>
    /// <param name="deathScreen">Active death screen component</param>
    /// <returns>Coroutine for respawn process</returns>
    private static IEnumerator HandleDeathAndRespawn(DeathScreen deathScreen)
    {
        try
        {
            // Trigger normal respawn
            deathScreen.NormalRespawnClick();
            yield return new WaitForSeconds(1f);

            // Navigate to next monolith after respawn
            yield return GoToNextMonolith();
            
            MelonLogger.Msg("Death and respawn sequence completed successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during death and respawn: {ex.Message}");
        }
    }

    /// <summary>
    /// Navigates to the next available monolith.
    /// Reused from original code but with better error handling.
    /// </summary>
    /// <returns>Coroutine for monolith navigation</returns>
    private static IEnumerator GoToNextMonolith()
    {
        try
        {
            // Find monolith stone
            var stone = FindHelpers.FindMonolithStone();
            if (stone == null)
            {
                MelonLogger.Warning("Monolith stone not found after respawn");
                yield break;
            }

            // Move to and interact with monolith stone
            yield return PlayerHelpers.MoveToForce(stone.transform.position);
            stone.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);

            // Find and select next available island
            yield return SelectNextMonolithIsland();

            // Wait for scene transition and initialize new area
            yield return WaitForSceneTransition();
            yield return CURRENT_ROUTINE?.OnNewArea();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error navigating to next monolith: {ex.Message}");
        }
    }

    /// <summary>
    /// Selects the next available monolith island based on completion status and type.
    /// </summary>
    /// <returns>Coroutine for island selection</returns>
    private static IEnumerator SelectNextMonolithIsland()
    {
        var islands = FindHelpers.FindMonolithIslands();
        if (islands.Count == 0)
        {
            MelonLogger.Warning("No monolith islands found");
            yield break;
        }

        // Find suitable island (not completed, correct type, connected to completed island)
        foreach (var (_, ui) in islands)
        {
            if (ui.island.completed)
                continue;

            // Only select Normal, Arena, or Beacon islands
            if (ui.island.islandType is not EchoWebIsland.IslandType.Normal 
                and not EchoWebIsland.IslandType.Arena 
                and not EchoWebIsland.IslandType.Beacon)
                continue;

            // Check if connected to a completed island
            if (HasConnectionToCompletedIsland(ui.island, islands))
            {
                var rewardType = ui.rewards.Count > 0 ? ui.rewards.getAtIndexOrFirst(0).rewardType : "NULL";
                MelonLogger.Msg($"Selected next monolith with reward: {rewardType}");
                
                ui.rightClicked();
                yield break;
            }
        }

        MelonLogger.Warning("No suitable monolith island found");
    }

    /// <summary>
    /// Checks if an island has connection to any completed island.
    /// </summary>
    /// <param name="island">Island to check</param>
    /// <param name="allIslands">All available islands</param>
    /// <returns>True if connected to completed island</returns>
    private static bool HasConnectionToCompletedIsland(EchoWebIsland island, Dictionary<int, EchoWebIslandUI> allIslands)
    {
        foreach (var connectionHex in island.connectedHexes)
        {
            var connectedIsland = allIslands.GetValueOrDefault(connectionHex);
            if (connectedIsland?.island.completed == true)
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Waits for scene transition to complete.
    /// </summary>
    /// <returns>Coroutine for scene waiting</returns>
    private static IEnumerator WaitForSceneTransition()
    {
        while (UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "M_Rest")
        {
            yield return new WaitForSeconds(1f);
        }
        yield return new WaitForSeconds(1f);
    }
}
