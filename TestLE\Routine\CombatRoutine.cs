﻿using System.Collections;
using UnityEngine;

namespace TestLE.Routines;

public abstract class CombatRoutine
{
    /// <summary>
    /// Health percentage to use potion at, 0f to disable, 0.5f to use at 50% health etc.
    /// Default is 0.6f.
    /// </summary>
    public virtual float PotionHealthUse => 0.6f;

    public virtual int CombatDistance => 10;

    public virtual int MovementSkillIndex => -1;

    public virtual bool PickupPotions => true;


    public virtual IEnumerator OnNewArea()
    {
        yield break;
    }

    public abstract IEnumerator Run(Enemy enemy, Transform enemyTransform, float distance);
}
