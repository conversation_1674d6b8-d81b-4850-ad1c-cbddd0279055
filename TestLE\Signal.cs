﻿namespace TestLE;

/// <summary>
/// A signal which can be waited for.
/// </summary>
public class Signal
{
    private TaskCompletionSource _signalTCS { get; set; } = new();
    private Dictionary<string, Action> _subscribers { get; } = new();


    /// <summary>
    /// Signal constructor.
    /// </summary>
    public Signal()
    {
        // Events.ScriptStop += ClearSubscribers;
    }

    /// <summary>
    /// Signal destructor.
    /// </summary>
    ~Signal()
    {
        // Events.ScriptStop -= ClearSubscribers;
    }

    /// <summary>
    /// Triggers the signal.
    /// </summary>
    public void Trigger()
    {
        // Reset the TaskCompletionSource so it can be awaited again
        var tcs = _signalTCS;
        _signalTCS = new TaskCompletionSource();

        tcs.SetResult();

        foreach (var Subscriber in _subscribers.Values)
            Subscriber.Invoke();
    }

    /// <summary>
    /// Waits for the signal.
    /// </summary>
    /// <param name="timeoutMilliseconds">Timeout in milliseconds. A value of -1 will wait indefinitely.</param>
    /// <param name="cancellationToken">Cancellation token to cancel the wait.</param>
    public void WaitForTrigger(int timeoutMilliseconds = -1, CancellationToken cancellationToken = default)
    {
        _signalTCS.Task.Wait(timeoutMilliseconds, cancellationToken);
    }

    /// <summary>
    /// Calls <paramref name="action"/> when the signal is triggered.
    /// </summary>
    /// <param name="action">Action to invoke when the signal is triggered.</param>
    public void Subscribe(Action action)
    {
        _subscribers.Add(action.Method.Name, action);
    }

    /// <summary>
    /// Calls <paramref name="action"/> when the signal is triggered.
    /// </summary>
    /// <param name="id">ID of the action.</param>
    /// <param name="action">Action to invoke when the signal is triggered.</param>
    public void Subscribe(string id, Action action)
    {
        _subscribers.Add(id, action);
    }

    /// <summary>
    /// Unsubscribes <paramref name="action"/> from the signal.
    /// </summary>
    /// <param name="action">Action to unsubscribe.</param>
    public void Unsubscribe(Action action)
    {
        _subscribers.Remove(action.Method.Name);
    }

    /// <summary>
    /// Unsubscribes <paramref name="id"/> from the signal.
    /// </summary>
    /// <param name="id">ID of the action to unsubscribe.</param>
    public void Unsubscribe(string id)
    {
        _subscribers.Remove(id);
    }

    /// <summary>
    /// Clears all subscribers.
    /// </summary>
    public void ClearSubscribers()
    {
        _subscribers.Clear();
    }
}

/// <summary>
/// A signal which can be waited for.
/// </summary>
/// <typeparam name="T">Type of the signal.</typeparam>
public class Signal<T>
{
    private TaskCompletionSource<T> _signalTCS { get; set; } = new();
    private Dictionary<string, Action<T>> _subscribers { get; } = new();


    /// <summary>
    /// Signal constructor.
    /// </summary>
    public Signal()
    {
        // Events.ScriptStop += ClearSubscribers;
    }

    /// <summary>
    /// Signal destructor.
    /// </summary>
    ~Signal()
    {
        // Events.ScriptStop -= ClearSubscribers;
    }

    /// <summary>
    /// Triggers the signal.
    /// </summary>
    /// <param name="result">Result of the signal.</param>
    public void Trigger(T result)
    {
        // Reset the TaskCompletionSource so it can be awaited again
        var tcs = _signalTCS;
        _signalTCS = new TaskCompletionSource<T>();

        tcs.SetResult(result);

        foreach (var Subscriber in _subscribers.Values)
            Subscriber.Invoke(result);
    }

    /// <summary>
    /// Waits for the signal.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token to cancel the wait.</param>
    /// <returns>Result of the signal.</returns>
    public T WaitForTrigger(CancellationToken cancellationToken = default)
    {
        _signalTCS.Task.Wait(cancellationToken);
        return _signalTCS.Task.Result;
    }

    /// <summary>
    /// Waits for the signal.
    /// </summary>
    /// <param name="timeoutMilliseconds">Timeout in milliseconds. A value of -1 will wait indefinitely.</param>
    /// <param name="cancellationToken">Cancellation token to cancel the wait.</param>
    /// <returns>Result of the signal.</returns>
    public T WaitForTrigger(int timeoutMilliseconds, CancellationToken cancellationToken = default)
    {
        _signalTCS.Task.Wait(timeoutMilliseconds, cancellationToken);
        return _signalTCS.Task.Result;
    }

    /// <summary>
    /// Waits for the signal until <paramref name="predicate"/> returns true.
    /// </summary>
    /// <param name="predicate">Predicate to check the result against.</param>
    /// <returns>Task which returns the result of the signal.</returns>
    public T WaitForTrigger(Predicate<T> predicate)
    {
        while (true)
        {
            var result = WaitForTrigger();
            if (predicate.Invoke(result))
                return result;
        }
    }

    /// <summary>
    /// Waits for the signal until <paramref name="predicate"/> returns true.
    /// </summary>
    /// <param name="predicate">Predicate to check the result against.</param>
    /// <param name="timeoutMilliseconds">Timeout in milliseconds for each try. A value of -1 will wait indefinitely.</param>
    /// <param name="tries">Amount of tries to wait for the signal. A value of -1 means infinite tries.</param>
    /// <returns>Result of the signal.</returns>
    public T? WaitForTrigger(Predicate<T?> predicate, int timeoutMilliseconds, int tries)
    {
        var curTries = 0;
        while (curTries++ <= tries || tries == -1)
        {
            var result = WaitForTrigger(timeoutMilliseconds);
            if (predicate.Invoke(result))
                return result;
        }

        return default;
    }

    /// <summary>
    /// Calls <paramref name="action"/> when the signal is triggered.
    /// </summary>
    /// <param name="action">Action to invoke when the signal is triggered.</param>
    public void Subscribe(Action<T> action)
    {
        _subscribers.Add(action.Method.Name, action);
    }

    /// <summary>
    /// Calls <paramref name="action"/> when the signal is triggered.
    /// </summary>
    /// <param name="id">ID of the action.</param>
    /// <param name="action">Action to invoke when the signal is triggered.</param>
    public void Subscribe(string id, Action<T> action)
    {
        _subscribers.Add(id, action);
    }

    /// <summary>
    /// Unsubscribes <paramref name="action"/> from the signal.
    /// </summary>
    /// <param name="action">Action to unsubscribe.</param>
    public void Unsubscribe(Action<T> action)
    {
        _subscribers.Remove(action.Method.Name);
    }

    /// <summary>
    /// Unsubscribes <paramref name="id"/> from the signal.
    /// </summary>
    /// <param name="id">ID of the action to unsubscribe.</param>
    public void Unsubscribe(string id)
    {
        _subscribers.Remove(id);
    }

    /// <summary>
    /// Clears all subscribers.
    /// </summary>
    public void ClearSubscribers()
    {
        _subscribers.Clear();
    }
}
