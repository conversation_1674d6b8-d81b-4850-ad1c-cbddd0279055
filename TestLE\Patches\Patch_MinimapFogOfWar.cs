﻿using Il2Cpp;
using <PERSON>on<PERSON>oa<PERSON>;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace TestLE.Patches;

// ReSharper disable once UnusedType.Global
public sealed class Patch_MinimapFogOfWar : Patch
{
    public override void Setup()
    {
        Patches_MinimapFogOfWar.OnInitializePostfix_ovl1 += OnInitializePostfix;
        Patches_MinimapFogOfWar.OnOnActiveSceneChangedPostfix_ovl1 += OnActiveSceneChangedPostfix;
    }
    
    private static void HandleFogOfWar(MinimapFogOfWar __instance)
    {
        WaitForPlayer(() => __instance.DrawRadiusAtPosition(Vector3.zero, 10000));
    }
    
    private static void OnInitializePostfix(MinimapFogOfWar __instance, MinimapFogOfWar.QuadScale scale, Vector3 position, bool clearAfterSwap, bool useSecondaryTextures, float customScale, bool isTown)
    {
        MelonLogger.Msg("OnInitializePostfix: Fog of war initialized!");
        HandleFogOfWar(__instance);
    }
    
    private static void OnActiveSceneChangedPostfix(MinimapFogOfWar __instance, Scene from, Scene to)
    {
        MelonLogger.Msg("OnActiveSceneChangedPostfix: Fog of war initialized!");
        HandleFogOfWar(__instance);
    }
}









// using System.Collections;
// using Il2Cpp;
// using MelonLoader;
// using UnityEngine;
// using UnityEngine.SceneManagement;
//
// namespace TestLE.Patches;
//
// public static class MinimapFogOfWarPatches
// {
//     // Auto-detects: MinimapFogOfWar.Initialize postfix
//     [AutoPatch]
//     public static void Initialize_Postfix(MinimapFogOfWar __instance, MinimapFogOfWar.QuadScale scale, Vector3 position, bool clearAfterSwap, bool useSecondaryTextures, float customScale, bool isTown)
//     {
//         HandleFogOfWar(__instance);
//     }
//
//     // Auto-detects: MinimapFogOfWar.OnActiveSceneChanged postfix  
//     [AutoPatch]
//     public static void OnActiveSceneChanged_Postfix(MinimapFogOfWar __instance, Scene scene1, Scene scene2)
//     {
//         HandleFogOfWar(__instance);
//     }
//
//     private static void HandleFogOfWar(MinimapFogOfWar __instance)
//     {
//         MelonCoroutines.Start(Wait(__instance));
//     }
//
//     private static IEnumerator Wait(MinimapFogOfWar __instance)
//     {
//         while (PLAYER == null)
//             yield return new WaitForSeconds(0.2f);
//
//         MelonLogger.Msg("Fog of war initialized!");
//         __instance.DrawRadiusAtPosition(Vector3.zero, 10000);
//         // __instance.DrawRadiusAtPosition(PLAYER.transform.position, 10000);
//     }
// }




// using System.Collections;
// using Il2Cpp;
// using HarmonyLib;
// using MelonLoader;
// using UnityEngine;
// using UnityEngine.SceneManagement;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(MinimapFogOfWar), "Initialize", typeof(MinimapFogOfWar.QuadScale), typeof(Vector3))]
// public class Patch_MinimapFogOfWar_Initialize
// {
//     public static void Postfix(MinimapFogOfWar __instance) => Patch_MinimapFogOfWar.Postfix(__instance);
// }
//
// [HarmonyPatch(typeof(MinimapFogOfWar), "OnActiveSceneChanged", typeof(Scene), typeof(Scene))]
// public class Patch_MinimapFogOfWar_OnActiveSceneChanged
// {
//     public static void Postfix(MinimapFogOfWar __instance) => Patch_MinimapFogOfWar.Postfix(__instance);
// }
//
// public static class Patch_MinimapFogOfWar
// {
//     public static void Postfix(MinimapFogOfWar __instance)
//     {
//         MelonCoroutines.Start(Wait(__instance));
//     }
//
//     private static IEnumerator Wait(MinimapFogOfWar __instance)
//     {
//         while (PLAYER == null)
//             yield return new WaitForSeconds(0.2f);
//
//         MelonLogger.Msg("Fog of war initialized!");
//         __instance.DrawRadiusAtPosition(Vector3.zero, 10000);
//         // __instance.DrawRadiusAtPosition(PLAYER.transform.position, 10000);
//     }
// }
