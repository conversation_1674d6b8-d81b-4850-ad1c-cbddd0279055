﻿using System.Collections;
using MelonLoader;
using TestLE.Routines.Actions;
using TestLE.Routines.Core;
using UnityEngine;

namespace TestLE.Routines;

/// <summary>
/// Refactored MainRoutine using modern C# principles and clean architecture.
///
/// Key improvements:
/// - Single Responsibility: Each action is handled by a dedicated class
/// - Open/Closed: New actions can be added without modifying existing code
/// - Dependency Inversion: Depends on abstractions (IGameAction) not concrete classes
/// - Configuration over hard-coding: Uses GameConfiguration for all constants
/// - Comprehensive error handling with graceful degradation
/// - Testable design with clear separation of concerns
///
/// The massive 180+ line MoveNext method has been replaced with a clean,
/// priority-based action system that is maintainable and extensible.
/// </summary>
public class MainRoutine : IEnumerator
{
    public object? Current { get; private set; }

    private readonly ActionPrioritySystem _actionSystem;
    private readonly IGameStateValidator _stateValidator;

    /// <summary>
    /// Initializes the MainRoutine with the new modular architecture.
    /// Sets up all action handlers with proper priority ordering.
    /// </summary>
    public MainRoutine()
    {
        try
        {
            // Initialize state validator
            _stateValidator = new GameStateManager();

            // Initialize all action handlers with their priorities
            var actions = new List<IGameAction>
            {
                new DeathHandler(),                    // Priority 1000 - Highest (critical)
                new MaterialStorageHandler(),          // Priority 900  - High (maintenance)
                new MonolithCompletionHandler(),       // Priority 800  - High (progression)
                new CombatHandler(),                   // Priority 600  - Medium-high (combat)
                new LootHandler(),                     // Priority 500  - Medium (collection)
                new IdleMovementHandler(),             // Priority 400  - Medium (maintenance)
                new InteractableHandler(),             // Priority 300  - Medium (beneficial)
                new ObjectiveHandler(),                // Priority 200  - Lower (progression)
                new FallbackHandler()                  // Priority 100  - Lowest (safety net)
            };

            // Initialize the priority system
            _actionSystem = new ActionPrioritySystem(actions, _stateValidator);

            MelonLogger.Msg("MainRoutine initialized with new modular architecture");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize MainRoutine: {ex.Message}");
            throw;
        }
    }


    /// <summary>
    /// The new, clean MoveNext implementation using the priority-based action system.
    ///
    /// This replaces the original 180+ line method with a simple, maintainable approach:
    /// 1. Validate game state once
    /// 2. Let the action system find the highest priority executable action
    /// 3. Execute that action
    /// 4. Handle errors gracefully
    ///
    /// The complex decision logic is now distributed across individual action classes,
    /// making the code much easier to understand, test, and maintain.
    /// </summary>
    /// <returns>True to continue coroutine execution, false to stop</returns>
    public bool MoveNext()
    {
        try
        {
            // Execute the highest priority action that can run
            var actionCoroutine = _actionSystem.ExecuteNextAction();

            if (actionCoroutine != null)
            {
                Current = actionCoroutine;
                return true;
            }

            // No action could execute - this should rarely happen due to FallbackHandler
            MelonLogger.Warning("No actions could execute - this indicates a system issue");
            Current = null;
            return false;
        }
        catch (Exception ex)
        {
            // Comprehensive error handling with graceful degradation
            MelonLogger.Error($"Critical error in MainRoutine.MoveNext: {ex.Message}");
            MelonLogger.Error($"Stack trace: {ex.StackTrace}");

            // Try to recover by returning a simple wait
            Current = Wait(1f);
            return true;
        }
    }

    /// <summary>
    /// Resets the coroutine state.
    /// Required by IEnumerator interface.
    /// </summary>
    public void Reset()
    {
        Current = null;
    }

    /// <summary>
    /// Simple wait coroutine for timing control.
    /// Used for error recovery and standard delays.
    /// </summary>
    /// <param name="duration">Wait duration in seconds</param>
    /// <returns>Wait coroutine</returns>
    private static IEnumerator Wait(float duration)
    {
        yield return new WaitForSeconds(duration);
    }

    /// <summary>
    /// Gets comprehensive system status for debugging and monitoring.
    /// Provides insight into the current state of all action handlers.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public string GetSystemStatus()
    {
        try
        {
            var status = new List<string>
            {
                _stateValidator.GetSystemHealthStatus(),
                LootHandler.GetLootStatus(),
                InteractableHandler.GetInteractableStatus(),
                ObjectiveHandler.GetObjectiveStatus(),
                FallbackHandler.GetFallbackStatus()
            };

            return string.Join(" | ", status);
        }
        catch (Exception ex)
        {
            return $"Error getting system status: {ex.Message}";
        }
    }

    /// <summary>
    /// Gets all registered actions for debugging.
    /// Useful for understanding the current action priority configuration.
    /// </summary>
    /// <returns>Read-only list of registered actions</returns>
    public IReadOnlyList<IGameAction> GetRegisteredActions()
    {
        return _actionSystem.GetRegisteredActions();
    }
}
