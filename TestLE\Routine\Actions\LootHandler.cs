using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles ground item looting when not in immediate combat.
/// Medium priority action to collect valuable items efficiently.
/// Follows Single Responsibility Principle - only handles looting logic.
/// </summary>
public class LootHandler : IGameAction
{
    public int Priority => 500; // Medium priority - important but not urgent
    public string Name => "Loot Handler";

    /// <summary>
    /// Checks if there are ground items to loot and no immediate combat threat.
    /// Ensures looting doesn't interfere with combat priorities.
    /// </summary>
    /// <returns>True if looting should be performed</returns>
    public bool CanExecute()
    {
        try
        {
            // Must have ground items to loot
            if (GROUND_ITEMS.Count == 0)
                return false;

            // Check if we're too close to enemies (combat takes priority)
            var (enemy, distance) = FindHelpers.FindNearestEnemy(
                PLAYER.transform.position, 
                GameConfiguration.Combat.EnemySearchRange);

            // Don't loot if enemy is very close (combat range)
            if (enemy != null && distance <= GameConfiguration.Combat.CloseCombatRange)
                return false;

            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking loot conditions: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes looting of the nearest ground item.
    /// Handles item validation and movement to item location.
    /// </summary>
    /// <returns>Coroutine for loot collection</returns>
    public IEnumerator Execute()
    {
        MelonLogger.Msg("Starting loot collection");

        try
        {
            yield return CollectNearestGroundItem();
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during loot collection: {ex.Message}");
        }
    }

    /// <summary>
    /// Collects the nearest ground item with proper validation.
    /// Handles null items and removes invalid entries from the list.
    /// </summary>
    /// <returns>Coroutine for item collection</returns>
    private static IEnumerator CollectNearestGroundItem()
    {
        var groundItem = GROUND_ITEMS.FirstOrDefault();
        if (groundItem == null)
        {
            MelonLogger.Warning("Ground item is null during collection");
            
            // Clean up null entry
            if (GROUND_ITEMS.Count > 0)
                GROUND_ITEMS.RemoveAt(0);
            
            yield break;
        }

        try
        {
            MelonLogger.Msg("Moving to ground item for collection");
            
            // Move to item location
            yield return groundItem.MoveToItem();
            
            // Pick up the item
            groundItem.Pickup();
            
            MelonLogger.Msg("Ground item collected successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error collecting ground item: {ex.Message}");
            
            // Remove problematic item from list to prevent infinite loops
            GROUND_ITEMS.Remove(groundItem);
        }
    }

    /// <summary>
    /// Collects all available ground items in sequence.
    /// Useful for situations like monolith completion where all items should be collected.
    /// </summary>
    /// <returns>Coroutine for collecting all items</returns>
    public static IEnumerator CollectAllGroundItems()
    {
        MelonLogger.Msg($"Collecting all ground items ({GROUND_ITEMS.Count} items)");

        while (GROUND_ITEMS.Count > 0)
        {
            yield return CollectNearestGroundItem();
            yield return new WaitForSeconds(GameConfiguration.Timing.ShortWaitTime);
        }

        MelonLogger.Msg("All ground items collected");
    }

    /// <summary>
    /// Gets the nearest ground item for distance calculations.
    /// Useful for other systems that need to know about nearby loot.
    /// </summary>
    /// <returns>Nearest ground item and distance, or null if none available</returns>
    public static (GroundItem? item, float distance) GetNearestGroundItem()
    {
        if (GROUND_ITEMS.Count == 0 || PLAYER == null)
            return (null, float.MaxValue);

        GroundItem? nearestItem = null;
        float nearestDistance = float.MaxValue;

        foreach (var item in GROUND_ITEMS)
        {
            if (item?.transform == null)
                continue;

            var distance = Vector3.Distance(PLAYER.transform.position, item.transform.position);
            if (distance < nearestDistance)
            {
                nearestDistance = distance;
                nearestItem = item;
            }
        }

        return (nearestItem, nearestDistance);
    }

    /// <summary>
    /// Cleans up null or invalid ground items from the list.
    /// Useful for maintenance and preventing errors.
    /// </summary>
    /// <returns>Number of items removed</returns>
    public static int CleanupInvalidGroundItems()
    {
        var initialCount = GROUND_ITEMS.Count;
        
        for (int i = GROUND_ITEMS.Count - 1; i >= 0; i--)
        {
            var item = GROUND_ITEMS[i];
            if (item == null || item.transform == null)
            {
                GROUND_ITEMS.RemoveAt(i);
            }
        }

        var removedCount = initialCount - GROUND_ITEMS.Count;
        if (removedCount > 0)
        {
            MelonLogger.Msg($"Cleaned up {removedCount} invalid ground items");
        }

        return removedCount;
    }

    /// <summary>
    /// Gets current loot status for debugging and monitoring.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public static string GetLootStatus()
    {
        var (nearestItem, distance) = GetNearestGroundItem();
        
        return $"Ground Items: {GROUND_ITEMS.Count}, " +
               $"Nearest: {(nearestItem != null ? $"{distance:F1}m" : "None")}";
    }
}
