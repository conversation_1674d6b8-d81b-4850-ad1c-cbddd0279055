using System.Collections;

namespace TestLE.Routines.Core;

/// <summary>
/// Represents a game action that can be executed with priority-based selection.
/// Each action encapsulates a specific behavior (combat, looting, movement, etc.)
/// following the Single Responsibility Principle.
/// </summary>
public interface IGameAction
{
    /// <summary>
    /// Priority of this action. Higher values = higher priority.
    /// Used by ActionPrioritySystem to determine execution order.
    /// </summary>
    int Priority { get; }

    /// <summary>
    /// Human-readable name for logging and debugging.
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Determines if this action can be executed in the current game state.
    /// Should be fast and side-effect free.
    /// </summary>
    /// <returns>True if action should be executed, false otherwise</returns>
    bool CanExecute();

    /// <summary>
    /// Executes the action as a Unity coroutine.
    /// Should handle its own error cases gracefully.
    /// </summary>
    /// <returns>Coroutine for Unity execution</returns>
    IEnumerator Execute();
}
