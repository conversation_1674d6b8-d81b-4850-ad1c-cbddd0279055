﻿using System.Collections;
using Il2Cpp;
using MelonLoader;
using UnityEngine;
using UnityEngine.AI;
using UnityEngine.SceneManagement;

namespace TestLE.Utilities;

public static class PlayerHelpers
{
    public static bool IsAbilityOnCooldown(int index)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return false;
        }

        return PLAYER.usingAbilityState.OnCooldown(BufferableAbilityType.Bar, index);
    }

    public static void UseAbility(int index, Transform targetTransform)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, index, targetTransform.position, targetTransform, true, true, true, true);
    }

    public static void UseMovementAbility(Transform targetTransform)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        UseAbility(CURRENT_ROUTINE.MovementSkillIndex, targetTransform);
    }

    public static void UseMovementAbility(Vector3 position)
    {
        if (CURRENT_ROUTINE == null || CURRENT_ROUTINE.MovementSkillIndex == -1 || IsAbilityOnCooldown(CURRENT_ROUTINE.MovementSkillIndex))
            return;

        PLAYER.usingAbilityState.UseBufferableAbilityCommand(BufferableAbilityType.Bar, CURRENT_ROUTINE.MovementSkillIndex, position, null, true, true, true, true);
    }

    public static void UsePortal()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        if (PLAYER.usingAbilityState == null)
        {
            MelonLogger.Msg("usingAbilityState is null");
            return;
        }

        PLAYER.usingAbilityState.UsePortalCommand(Vector3.zero, true);
    }

    public static bool UsePotion()
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return false;
        }

        if (PLAYER.healthPotion.currentCharges <= 0)
        {
            MelonLogger.Msg("No potions available");
            return false;
        }

        PLAYER.PotionKeyPressed();
        return true;
    }

    /// <summary>
    /// Will try once to move to the position.
    /// </summary>
    /// <param name="position">Position to move to</param>
    public static void MoveTo(Vector3 position)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            return;
        }

        Move(position);
    }

    /// <summary>
    /// Will force the player to move to the position, looping until the player is within the stoppingDistance or maxTries is reached.
    /// </summary>
    /// <param name="position">Position to move to</param>
    /// <param name="stoppingDistance">Distance to stop moving</param>
    /// <param name="maxTries">Max number of tries</param>
    /// <param name="delayBetweentries">Delay between tries</param>
    /// <returns>IEnumerator for coroutine</returns>
    public static IEnumerator MoveToForce(Vector3 position, float stoppingDistance = 1f, int maxTries = 15, float delayBetweentries = 0.3333f)
    {
        if (PLAYER == null)
        {
            MelonLogger.Msg("PLAYER is null");
            yield break;
        }

        for (var i = 0; i < maxTries; i++)
        {
            if (Vector3.Distance(PLAYER.transform.position, position) <= stoppingDistance)
                break;

            Move(position);
            yield return new WaitForSeconds(delayBetweentries);
        }
    }

    private static void Move(Vector3 position)
    {
        if (!NavMesh.SamplePosition(position, out var hit, 10f, -1))
        {
            MelonLogger.Msg("Failed to find a valid move position");
            return;
        }

        var path = new NavMeshPath();
        PLAYER.navMeshAgent.CalculatePath(hit.position, path);
        // NavMesh.CalculatePath(PLAYER.transform.position, hit.position, -1, path);
        if (path.corners.Length == 0)
        {
            MelonLogger.Msg("Failed to find a valid path, defaulting to full path move");
            InternalMove(hit.position);
            return;
        }

        // // If in monolith rest area, just move to the position
        // if (SceneManager.GetActiveScene().name == "M_Rest")
        // {
        //     InternalMove(hit.position);
        //     return;
        // }

        foreach (var c in path.corners) // test
        {
            var distance = Vector3.Distance(PLAYER.transform.position, c);
            if (distance is >= 10f and <= 20f && !IsAbilityOnCooldown(CURRENT_ROUTINE!.MovementSkillIndex))
            {
                UseMovementAbility(c);
                break;
            }

            if (distance is >= 5f and <= 20f)
            {
                InternalMove(c);
                return;
            }
        }

        // If no close corners found, default to full path move
        InternalMove(hit.position);


        // foreach (var c in PLAYER.navMeshAgent.path.corners) // test
        // {
        //     if (Vector3.Distance(PLAYER.transform.position, c) < 5f)
        //         break;
        //
        //     UseMovementAbility(c);
        //     break;
        // }

        // var directionToPlayer = (PLAYER!.transform.position - position).normalized;
        // var targetPosition = position - directionToPlayer * (PLAYER.navMeshAgent.radius + stoppingDistance);
        //
        // PLAYER.movingState.MoveToPointNoChecks(targetPosition, true);
    }

    private static void InternalMove(Vector3 position)
    {
        // PLAYER!.movingState.MouseClickMoveCommand(CAMERA.ScreenPointToRay(CAMERA.WorldToScreenPoint(position)), false, 1f, true, position, true);
        PLAYER!.movingState.MoveToPointNoChecks(position, true);
    }

    public static void StoreMaterials()
    {
        if (STORE_MATERIALS_BUTTON == null)
        {
            MelonLogger.Msg("STORE_MATERIALS_BUTTON is null");
            return;
        }

        STORE_MATERIALS_BUTTON.onClick.Invoke();
    }
}
