﻿using Il2Cpp;
using Il2CppLE.Interactions.KeyProviders;
using Il2CppLE.UI.Bazaar;
using Il2CppNetworking.Multiplayer.Interactables.Portals;
using MelonLoader;
using UnityEngine;
using UnityEngine.UI;

namespace TestLE.Utilities;

public static class FindHelpers
{
    private static DroppableRewardType[] _monolithRewardPriority { get; } =
    {
        DroppableRewardType.UniqueItemMultipleTypes,
        DroppableRewardType.UniqueItemSingleType,
        DroppableRewardType.ExaltedItemMultipleTypes,
        DroppableRewardType.ExaltedItemSingleType,
        DroppableRewardType.Idols,
        DroppableRewardType.ExperiencePotions,
        DroppableRewardType.Gold
    };


    public static Button? FindStoreMaterialsButton()
    {
        return Resources.FindObjectsOfTypeAll<StoreMaterialsButton>().FirstOrDefault(x => x.gameObject.scene.isLoaded)?.GetComponent<Button>();
        // return GameObject.Find("CleanUpCraftingItems").GetComponent<Button>();
        // return UnityEngine.Object.FindObjectOfType<StoreMaterialsButton>().GetComponent<Button>();
    }

    public static List<ChangeIconBasedOnQuest> FindQuestObjects()
    {
        return UnityEngine.Object.FindObjectsOfType<ChangeIconBasedOnQuest>().ToList();
    }

    public static List<TransitionOrGateInteraction> FindSceneTransitions()
    {
        return Resources.FindObjectsOfTypeAll<TransitionOrGateInteraction>().Where(x => x.gameObject.scene.isLoaded).ToList();
    }

    public static OpenStash? FindStashOpener()
    {
        return UnityEngine.Object.FindObjectOfType<OpenStash>();
    }
    
    public static StashTabbedUIControls? FindStashTabUI()
    {
        var obj = GameObject.Find("GUI/Canvas/Stash Panel Holder/StashPanelExpandable(Clone)/left-container/Stash");
        return obj != null ? obj.GetComponent<StashTabbedUIControls>() : null;
    }

    public static DeathScreen? FindDeathScreen()
    {
        var obj = GameObject.Find("GUI/Canvas/Death Panel Holder/Death(Clone)");
        return obj != null ? obj.GetComponent<DeathScreen>() : null;
    }

    public static InventoryContainerUI? FindInventoryUI()
    {
        var obj = GameObject.Find("GUI/Canvas/Inventory Panel Holder/InventoryPanel(Clone)/InventoryHolder/Inventory/Inventory Container");
        return obj != null ? obj.GetComponent<InventoryContainerUI>() : null;
    }

    public static StashTabsNavigable? FindStashNavigable()
    {
        var obj = GameObject.Find("GUI/Canvas/Stash Panel Holder/StashPanelExpandable(Clone)/left-container/StashTabs/Viewport/TabHolder");
        return obj != null ? obj.GetComponent<StashTabsNavigable>() : null;
    }

    public static BazaarUI? FindBazaarUI()
    {
        return Resources.FindObjectsOfTypeAll<BazaarUI>().FirstOrDefault(x => x.gameObject.scene.isLoaded);
    }

    public static List<ShrineVisualsCreator> FindShrines()
    {
        return UnityEngine.Object.FindObjectsOfType<ShrineVisualsCreator>().ToList();
    }

    public static List<GroundXPTomeVisuals> FindGroundXPTomes()
    {
        return UnityEngine.Object.FindObjectsOfType<GroundXPTomeVisuals>().ToList();
    }

    public static List<WorldObjectClickListener> FindInteractables()
    {
        return UnityEngine.Object.FindObjectsOfType<WorldObjectClickListener>().ToList();
    }

    public static Button FindMonolithDifficultyButton(MonolithDifficulty difficulty)
    {
        var difficultyString = difficulty switch
        {
            MonolithDifficulty.Normal => "MonolithDifficultyNormal",
            MonolithDifficulty.Empowered => "MonolithDifficultyLegendary",
            _ => throw new ArgumentOutOfRangeException(nameof(difficulty), difficulty, null)
        };

        var difficultyButton = GameObject.Find($"GUI/Canvas/Monolith Holder/Monolith Panel(Clone)/container (1)/Difficulty Selection/MonolithLevelSelect/{difficultyString}/ChooseDifficultyButton");
        if (difficultyButton == null)
        {
            MelonLogger.Msg($"MonolithDifficulty button {difficultyString} not found");
            return null!;
        }

        return difficultyButton.GetComponent<Button>();
    }

    public static WorldObjectClickListener? FindMonolithStone()
    {
        var stones = UnityEngine.Object.FindObjectsOfType<OpenMonolithPanelInteraction>().ToList();
        var closestStone = stones.GetClosestToPlayer();

        return closestStone != null ? closestStone.transform.parent.GetComponentInChildren<WorldObjectClickListener>() : null;


        // var stones = UnityEngine.Object.FindObjectsOfType<OpenMonolithPanelInteraction>();
        // if (stones.Count == 0)
        // {
        //     MelonLogger.Msg("OpenMonolithPanelInteraction not found");
        //     return null;
        // }
        //
        // Transform closestStone = null!;
        // var minDistance = float.MaxValue;
        //
        // foreach (var stone in stones)
        // {
        //     var stoneTransform = stone.transform;
        //     var distance = Vector3.Distance(stoneTransform.position, PLAYER.transform.position);
        //     if (distance < minDistance)
        //     {
        //         minDistance = distance;
        //         closestStone = stoneTransform;
        //     }
        // }
        //
        // return closestStone.parent.GetComponentInChildren<WorldObjectClickListener>();

        // var stone = UnityEngine.Object.FindObjectOfType<OpenMonolithPanelInteraction>();
        // if (stone == null)
        // {
        //     MelonLogger.Msg("OpenMonolithPanelInteraction not found");
        //     return null;
        // }
        //
        // return stone.transform.parent.GetComponentInChildren<WorldObjectClickListener>();
    }

    public static Dictionary<int, EchoWebIslandUI> FindMonolithIslands()
    {
        return UnityEngine.Object.FindObjectsOfType<EchoWebIslandUI>()
            .OrderBy(i =>
            {
                if (i.rewards.Count == 0)
                    return int.MaxValue;

                var index = Array.IndexOf(_monolithRewardPriority, i.rewards.getAtIndexOrFirst(0).rewardType);
                return index == -1 ? int.MaxValue : index;
            }).ToDictionary(i => i.island.hexIndex, i => i);

        // return UnityEngine.Object.FindObjectsOfType<EchoWebIslandUI>().ToDictionary(x => x.island.hexIndex)
        //     .OrderBy(i =>
        //     {
        //         if (i.Value.rewards.Count == 0)
        //             return int.MaxValue;
        //
        //         var index = Array.IndexOf(_monolithRewardPriority, i.Value.rewards.getAtIndexOrFirst(0).rewardType);
        //         return index == -1 ? int.MaxValue : index;
        //     }).ToDictionary(i => i.Key, i => i.Value);
    }

    public static GameObject? FindMonolithCompleteButton()
    {
        return GameObject.Find("MonolithComplete");
    }

    public static (WorldObjectClickListener? obj, bool isActive) FindMonolithCompleteRewardChest()
    {
        var chest = UnityEngine.Object.FindObjectOfType<MonolithRewardChestKeyProvider>();
        if (chest == null)
        {
            MelonLogger.Msg("MonolithRewardChestKeyProvider not found");
            return (null, false);
        }

        return (chest.gameObject.GetComponent<WorldObjectClickListener>(), chest.gameObject.GetComponent<OutlineOnMouseOver>().enabled);
    }

    public static (WorldObjectClickListener? obj, bool isActive) FindMonolithCompleteRewardRock()
    {
        var rock = GameObject.Find("Reward Rock(Clone)");
        if (rock == null)
        {
            MelonLogger.Msg("Reward Rock(Clone) not found");
            return (null, false);
        }

        var rockInteract = rock.GetComponentInChildren<WorldObjectClickListener>();
        return (rockInteract, rockInteract.gameObject.GetComponent<OutlineOnMouseOver>().enabled);
    }

    public static WorldObjectClickListener? FindMonolithPortal()
    {
        var portal = UnityEngine.Object.FindObjectOfType<PortalVisualController>();
        if (portal == null)
        {
            MelonLogger.Msg("PortalVisualController not found");
            return null;
        }

        return portal.GetComponentInChildren<WorldObjectClickListener>();
    }

    public static (Enemy? enemy, float distance) FindNearestEnemy(Vector3 position, float maxDistance)
    {
        Enemy? nearestEnemy = null;
        var lastDistance = float.MaxValue;

        for (var i = 0; i < ENEMIES.Count; i++)
        {
            var enemy = ENEMIES[i];
            if (enemy == null)
            {
                ENEMIES.RemoveAt(i);
                i--;
                continue;
            }

            if (enemy.Data == null)
            {
                enemy.RemoveEnemy();
                i--;
                continue;
            }

            var distance = Vector3.Distance(enemy.Data.transform.position, position);
            if (distance <= maxDistance && distance < lastDistance)
            {
                lastDistance = distance;
                nearestEnemy = enemy;
            }
        }

        return (nearestEnemy, lastDistance);

        // return ENEMIES.Where(x => !x.isDead && Vector3.Distance(x.data.transform.position, position) <= maxDistance).MinBy(x => Vector3.Distance(x.data.transform.position, position));
    }

    public static List<ListingUI> FindBazaarListings()
    {
        return UnityEngine.Object.FindObjectsOfType<ListingUI>().ToList();
    }


    public enum MonolithDifficulty
    {
        Normal,
        Empowered
    }
}
