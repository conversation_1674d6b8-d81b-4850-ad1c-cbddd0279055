Il2Cpp.MinimapFogOfWar.Initialize -> Postfix
Il2Cpp.MinimapFogOfWar.OnActiveSceneChanged -> Postfix

Il2CppLE.Gameplay.Monolith.Frontend.MonolithPulse.OnDestroyInternal -> Postfix
Il2CppLE.Gameplay.Monolith.Frontend.MonolithPulse.ReceiveHide -> Postfix
Il2CppLE.Gameplay.Monolith.Frontend.MonolithPulse.ReceiveShow -> Postfix

Il2Cpp.GroundPotionLabel.SetGroundTooltipText -> Postfix
Il2Cpp.GroundGoldLabel.SetGroundTooltipText -> Postfix

Il2Cpp.EchoLoadingStatusUI.OnEchoLoadingFinished -> Postfix

Il2CppLE.Interactions.KeyProviders.DungeonExitKeyProvider.GetMessageKey -> Postfix

Il2CppLE.Services.Bazaar.BazaarClientService.HandleBazaarResponse -> Postfix

Il2Cpp.ActorVisuals.initialiseAlignment -> Postfix
Il2Cpp.ActorVisuals.OnDestroy -> Postfix