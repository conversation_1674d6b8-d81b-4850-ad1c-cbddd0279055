using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles combat with enemies using the configured combat routine.
/// Medium-high priority action for engaging enemies within combat range.
/// Follows Single Responsibility Principle - only handles combat logic.
/// </summary>
public class CombatHandler : IGameAction
{
    public int Priority => 600; // Medium-high priority - combat is important but not urgent
    public string Name => "Combat Handler";

    private readonly Dictionary<Enemy, int> _enemyInactiveFailSafe = new();

    /// <summary>
    /// Checks if there's an enemy within combat range that requires engagement.
    /// Uses configured combat distance from the current routine.
    /// </summary>
    /// <returns>True if combat should be initiated</returns>
    public bool CanExecute()
    {
        try
        {
            if (CURRENT_ROUTINE == null)
                return false;

            var (enemy, distance) = FindHelpers.FindNearestEnemy(
                PLAYER.transform.position, 
                GameConfiguration.Combat.EnemySearchRange);

            return enemy != null && distance <= CURRENT_ROUTINE.CombatDistance;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking combat conditions: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes combat routine with the nearest enemy.
    /// Handles enemy validation and delegates to combat routine.
    /// </summary>
    /// <returns>Coroutine for combat execution</returns>
    public IEnumerator Execute()
    {
        var (enemy, distance) = FindHelpers.FindNearestEnemy(
            PLAYER.transform.position, 
            GameConfiguration.Combat.EnemySearchRange);

        if (enemy == null)
        {
            MelonLogger.Warning("No enemy found during combat execution");
            yield break;
        }

        MelonLogger.Msg($"Engaging enemy at distance {distance:F1}");
        yield return ExecuteCombatRoutine(enemy, distance);
    }

    /// <summary>
    /// Executes the combat routine with proper enemy validation and error handling.
    /// Handles inactive enemies and delegates to the configured combat routine.
    /// </summary>
    /// <param name="enemy">Target enemy</param>
    /// <param name="distance">Distance to enemy</param>
    /// <returns>Coroutine for combat routine</returns>
    private IEnumerator ExecuteCombatRoutine(Enemy enemy, float distance)
    {
        try
        {
            // Validate enemy state
            if (!IsEnemyValid(enemy, distance))
            {
                yield break;
            }

            var enemyTransform = enemy.Data.transform;

            // Execute combat if enemy is active
            if (enemy.Data.gameObject.active)
            {
                yield return CURRENT_ROUTINE!.Run(enemy, enemyTransform, distance);
            }
            else
            {
                // Handle inactive enemy
                yield return HandleInactiveEnemy(enemy, enemyTransform, distance);
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error during combat routine: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates enemy state and handles special cases.
    /// Removes enemies that are too close and inactive (except special enemies).
    /// </summary>
    /// <param name="enemy">Enemy to validate</param>
    /// <param name="distance">Distance to enemy</param>
    /// <returns>True if enemy is valid for combat</returns>
    private bool IsEnemyValid(Enemy enemy, float distance)
    {
        // Special validation for close inactive enemies (except Exiled Mage)
        if (distance <= GameConfiguration.Combat.CloseCombatRange && 
            enemy.Data.Data.actorName != "Exiled Mage" && 
            (!enemy.Data.actorSync.gameObject.active || !enemy.Data.isActiveAndEnabled))
        {
            MelonLogger.Msg("Enemy is too close and not active - removing");
            enemy.RemoveEnemy();
            return false;
        }

        return true;
    }

    /// <summary>
    /// Handles combat with inactive enemies.
    /// Implements fail-safe mechanism to prevent infinite loops with broken enemies.
    /// </summary>
    /// <param name="enemy">Inactive enemy</param>
    /// <param name="enemyTransform">Enemy transform</param>
    /// <param name="distance">Distance to enemy</param>
    /// <returns>Coroutine for inactive enemy handling</returns>
    private IEnumerator HandleInactiveEnemy(Enemy enemy, Transform enemyTransform, float distance)
    {
        if (distance <= GameConfiguration.Combat.CloseCombatRange)
        {
            // Track inactive attempts for fail-safe
            _enemyInactiveFailSafe[enemy] = _enemyInactiveFailSafe.GetValueOrDefault(enemy) + 1;
            
            if (_enemyInactiveFailSafe[enemy] >= GameConfiguration.Combat.MaxInactiveEnemyAttempts)
            {
                MelonLogger.Msg($"Enemy inactive for {GameConfiguration.Combat.MaxInactiveEnemyAttempts} attempts - removing");
                enemy.RemoveEnemy();
                _enemyInactiveFailSafe.Remove(enemy);
                yield break;
            }
        }

        // Move towards inactive enemy
        PlayerHelpers.MoveTo(enemyTransform.position);
        yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
    }

    /// <summary>
    /// Cleans up tracking data for removed enemies.
    /// Should be called when enemies are removed from the game.
    /// </summary>
    /// <param name="enemy">Enemy that was removed</param>
    public void OnEnemyRemoved(Enemy enemy)
    {
        _enemyInactiveFailSafe.Remove(enemy);
    }

    /// <summary>
    /// Gets current fail-safe status for debugging.
    /// </summary>
    /// <returns>Dictionary of enemy fail-safe counts</returns>
    public IReadOnlyDictionary<Enemy, int> GetFailSafeStatus()
    {
        return _enemyInactiveFailSafe.AsReadOnly();
    }

    /// <summary>
    /// Clears all fail-safe tracking data.
    /// Useful for area transitions or when resetting combat state.
    /// </summary>
    public void ClearFailSafeData()
    {
        _enemyInactiveFailSafe.Clear();
        MelonLogger.Msg("Combat fail-safe data cleared");
    }
}
