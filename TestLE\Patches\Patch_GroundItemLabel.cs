﻿// using System.Collections;
// using HarmonyLib;
// using Il2Cpp;
// using Il2CppItemFiltering;
// using <PERSON>onLoader;
// using UnityEngine;
//
// namespace TestLE.Patches;
//
// [HarmonyPatch(typeof(GroundItemLabel))]
// public class Patch_GroundItemLabel
// {
//     [HarmonyPostfix]
//     [HarmonyPatch("initialise")]
//     private static void initialise_Postfix(GroundItemLabel __instance)
//     {
//         MelonCoroutines.Start(WaitForLootFilter(__instance));
//     }
//
//     private static IEnumerator WaitForLootFilter(GroundItemLabel __instance)
//     {
//         yield return new WaitForSeconds(1f);
//
//         if (__instance.lastFilterResult == Rule.RuleOutcome.HIDE || !__instance.gameObject.active || !CheckItem(__instance.getItemData()))
//             yield break;
//
//         _ = new GroundItem(__instance);
//     }
//
//     private static bool CheckItem(ItemDataUnpacked itemData)
//     {
//         if (!itemData.isUnique() || itemData.legendaryPotential > 0)
//             return true;
//
//         return itemData.FullName switch
//         {
//             "Cradle of the Erased" when itemData.weaversWill >= 15 => true,
//             "Communion of the Erased" when itemData.weaversWill >= 17 => true,
//             "Advent of the Erased" when itemData.weaversWill >= 19 => true,
//             "Swaddling of the Erased" when itemData.weaversWill >= 14 => true,
//             "Ambitions of an Erased Acolyte" when itemData.weaversWill >= 8 => true,
//             "Code of an Erased Sentinel" when itemData.weaversWill >= 12 => true,
//             "Gambit of an Erased Rogue" when itemData.weaversWill >= 15 => true,
//             "Dedication of an Erased Primalist" when itemData.weaversWill >= 17 => true,
//             "Knowledge of an Erased Mage" when itemData.weaversWill >= 17 => true,
//             "Font of the Erased" when itemData.weaversWill >= 18 => true,
//             "Immolator's Oblation" => true,
//             "Jungle Queen's Chaps of Holding" => true,
//             "Shattered Chains" => true,
//             "Strands of Souls" => true,
//             "Core of the Mountain" => true,
//             "Vaion's Chariot" => true,
//             "Fractured Crown" => true,
//             "Aurora's Time Glass" => true,
//             "Gambler's Fallacy" => true,
//             "Omnis" => true,
//             "Orian's Eye" => true,
//             "Soul Gambler's Fallacy" => true,
//             "Anchor of Oblivion" => true,
//             "Oceareon" => true,
//             "Red Ring of Atlaria" => true,
//             "Bastion of Honour" => true,
//             "Ravenous Void" => true,
//             _ => false
//         };
//     }
// }
//
//
// // [HarmonyPatch(typeof(GroundItemLabel), "lastFilterResult", MethodType.Getter)] // gives error in console
// // public class Patch_GroundItemLabel_lastFilterResult_Getter
// // {
// //     public static void Postfix(GroundItemLabel __instance, ref Rule.RuleOutcome __result)
// //     {
// //         MelonLogger.Msg($"GroundItemLabel lastFilterResult_Getter: {__result}");
// //
// //         if (__result != Rule.RuleOutcome.HIDE)
// //             _ = new GroundItem(__instance);
// //     }
// // }
