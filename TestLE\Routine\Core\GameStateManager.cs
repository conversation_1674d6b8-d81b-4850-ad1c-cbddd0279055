using MelonLoader;

namespace TestLE.Routines.Core;

/// <summary>
/// Centralized game state validation and management.
/// Implements the Single Responsibility Principle by handling only state validation.
/// Replaces scattered null checks throughout the original code.
/// </summary>
public class GameStateManager : IGameStateValidator
{
    /// <summary>
    /// Validates all critical game state components.
    /// Provides detailed error information for debugging.
    /// </summary>
    /// <returns>Comprehensive validation result</returns>
    public GameStateValidationResult ValidateGameState()
    {
        // Check main routine validity
        if (!IsMainRoutineValid())
        {
            return GameStateValidationResult.Failure("MainRoutine is not the current routine or is null");
        }

        // Check player validity
        if (!IsPlayerValid())
        {
            return GameStateValidationResult.Failure("Player is null or invalid");
        }

        // Check combat routine validity
        if (!IsCombatRoutineValid())
        {
            return GameStateValidationResult.Failure("Current combat routine is null");
        }

        return GameStateValidationResult.Success();
    }

    /// <summary>
    /// Validates main routine state.
    /// Checks both existence and active status.
    /// </summary>
    /// <returns>True if main routine is valid and active</returns>
    public bool IsMainRoutineValid()
    {
        try
        {
            return MAIN_ROUTINE != null && MAIN_ROUTINE == Globals.MAIN_ROUTINE;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error validating main routine: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Validates player object and essential components.
    /// Ensures player is not null and has required components.
    /// </summary>
    /// <returns>True if player is valid and ready</returns>
    public bool IsPlayerValid()
    {
        try
        {
            if (PLAYER == null)
            {
                return false;
            }

            // Additional validation for critical player components
            if (PLAYER.transform == null)
            {
                MelonLogger.Warning("Player transform is null");
                return false;
            }

            if (PLAYER.movingState?.myAgent == null)
            {
                MelonLogger.Warning("Player movement agent is null");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error validating player: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Validates current combat routine availability.
    /// Essential for combat-related actions.
    /// </summary>
    /// <returns>True if combat routine is available</returns>
    public bool IsCombatRoutineValid()
    {
        try
        {
            return CURRENT_ROUTINE != null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error validating combat routine: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Validates specific game collections are accessible.
    /// Used by actions that depend on specific game object lists.
    /// </summary>
    /// <returns>True if collections are accessible</returns>
    public bool AreGameCollectionsValid()
    {
        try
        {
            // These should not be null (they're initialized as empty lists)
            return ENEMIES != null && 
                   GROUND_ITEMS != null && 
                   INTERACTABLES != null && 
                   MONOLITH_OBJECTIVES != null && 
                   GOOD_SHRINES != null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error validating game collections: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Performs a quick health check of all critical systems.
    /// Used for monitoring and debugging purposes.
    /// </summary>
    /// <returns>Detailed health status</returns>
    public string GetSystemHealthStatus()
    {
        var status = new List<string>();

        status.Add($"Main Routine: {(IsMainRoutineValid() ? "OK" : "FAIL")}");
        status.Add($"Player: {(IsPlayerValid() ? "OK" : "FAIL")}");
        status.Add($"Combat Routine: {(IsCombatRoutineValid() ? "OK" : "FAIL")}");
        status.Add($"Collections: {(AreGameCollectionsValid() ? "OK" : "FAIL")}");

        if (PLAYER != null)
        {
            status.Add($"Player Position: {PLAYER.transform.position}");
            status.Add($"Player Velocity: {PLAYER.movingState?.myAgent?.velocity.magnitude:F2}");
        }

        status.Add($"Enemies: {ENEMIES?.Count ?? 0}");
        status.Add($"Ground Items: {GROUND_ITEMS?.Count ?? 0}");
        status.Add($"Interactables: {INTERACTABLES?.Count ?? 0}");

        return string.Join(" | ", status);
    }
}
