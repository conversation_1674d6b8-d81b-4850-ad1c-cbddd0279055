using System.Collections;
using MelonLoader;
using TestLE.Routines.Core;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Routines.Actions;

/// <summary>
/// Handles monolith objectives including enemy and clickable objectives.
/// Lower priority action for progressing through monolith objectives.
/// Follows Single Responsibility Principle - only handles objective logic.
/// </summary>
public class ObjectiveHandler : IGameAction
{
    public int Priority => 200; // Lower priority - objectives are important but not urgent
    public string Name => "Objective Handler";

    /// <summary>
    /// Checks if there are valid monolith objectives to pursue.
    /// Validates objectives and removes invalid ones.
    /// </summary>
    /// <returns>True if objectives should be pursued</returns>
    public bool CanExecute()
    {
        try
        {
            // Clean up null objectives first
            CleanupNullObjectives();

            // Must have objectives to pursue
            if (MONOLITH_OBJECTIVES.Count == 0)
                return false;

            // Check if first objective is valid
            var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
            return objective != null && IsObjectiveValid(objective);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error checking objective conditions: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Executes movement toward the current monolith objective.
    /// Handles both enemy and clickable objectives.
    /// </summary>
    /// <returns>Coroutine for objective pursuit</returns>
    public IEnumerator Execute()
    {
        var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        if (objective == null)
        {
            MelonLogger.Warning("No objective found during execution");
            yield break;
        }

        try
        {
            // Try enemy objective first
            var enemyObjective = objective.GetEnemyObjective();
            if (enemyObjective != null)
            {
                yield return HandleEnemyObjective(enemyObjective);
                yield break;
            }

            // Try clickable objective
            var clickObjective = objective.GetClickObjective();
            if (clickObjective != null)
            {
                yield return HandleClickObjective(clickObjective);
                yield break;
            }

            // Objective is invalid - remove it
            MelonLogger.Warning("Objective has no valid enemy or click target - removing");
            MONOLITH_OBJECTIVES.Remove(objective);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error handling objective: {ex.Message}");
            // Remove problematic objective
            MONOLITH_OBJECTIVES.Remove(objective);
        }
    }

    /// <summary>
    /// Handles movement toward an enemy objective.
    /// Simply moves toward the enemy position for combat engagement.
    /// </summary>
    /// <param name="enemyObjective">Target enemy visual</param>
    /// <returns>Coroutine for enemy objective handling</returns>
    private static IEnumerator HandleEnemyObjective(ActorVisuals enemyObjective)
    {
        MelonLogger.Msg("Moving toward enemy objective");

        try
        {
            PlayerHelpers.MoveTo(enemyObjective.transform.position);
            yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error handling enemy objective: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles interaction with a clickable objective.
    /// Moves to the objective and clicks it if within range.
    /// </summary>
    /// <param name="clickObjective">Target clickable object</param>
    /// <returns>Coroutine for click objective handling</returns>
    private static IEnumerator HandleClickObjective(WorldObjectClickListener clickObjective)
    {
        MelonLogger.Msg("Moving toward clickable objective");

        try
        {
            var objectiveTransform = clickObjective.transform;
            var objectivePosition = objectiveTransform.position;

            // Move to objective
            PlayerHelpers.MoveTo(objectivePosition);
            yield return new WaitForSeconds(GameConfiguration.Timing.StandardWaitTime);

            // Check if in interaction range and click
            var distance = Vector3.Distance(PLAYER.transform.position, objectiveTransform.position);
            if (distance <= clickObjective.interactionRange)
            {
                MelonLogger.Msg($"Clicking objective at distance {distance:F1}");
                clickObjective.ObjectClick(PLAYER.gameObject, true);
            }
            else
            {
                MelonLogger.Msg($"Objective too far ({distance:F1} > {clickObjective.interactionRange}) - will retry");
            }
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error handling click objective: {ex.Message}");
        }
    }

    /// <summary>
    /// Validates if an objective is still valid and accessible.
    /// </summary>
    /// <param name="objective">Objective to validate</param>
    /// <returns>True if objective is valid</returns>
    private static bool IsObjectiveValid(MonolithObjective objective)
    {
        try
        {
            // Check if objective has valid targets
            var enemyObjective = objective.GetEnemyObjective();
            var clickObjective = objective.GetClickObjective();

            return enemyObjective != null || clickObjective != null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error validating objective: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Cleans up null objectives from the list.
    /// </summary>
    private static void CleanupNullObjectives()
    {
        for (int i = MONOLITH_OBJECTIVES.Count - 1; i >= 0; i--)
        {
            if (MONOLITH_OBJECTIVES[i] == null)
            {
                MONOLITH_OBJECTIVES.RemoveAt(i);
            }
        }
    }

    /// <summary>
    /// Gets current objective status for debugging.
    /// </summary>
    /// <returns>Formatted status string</returns>
    public static string GetObjectiveStatus()
    {
        CleanupNullObjectives();
        
        if (MONOLITH_OBJECTIVES.Count == 0)
            return "No objectives";

        var objective = MONOLITH_OBJECTIVES.FirstOrDefault();
        if (objective == null)
            return "Invalid objective";

        var enemyObjective = objective.GetEnemyObjective();
        var clickObjective = objective.GetClickObjective();

        var type = enemyObjective != null ? "Enemy" : 
                   clickObjective != null ? "Click" : "Unknown";

        return $"Objectives: {MONOLITH_OBJECTIVES.Count}, Current: {type}";
    }

    /// <summary>
    /// Forces cleanup of all objectives.
    /// Useful for area transitions or when objectives become invalid.
    /// </summary>
    public static void ClearAllObjectives()
    {
        MONOLITH_OBJECTIVES.Clear();
        MelonLogger.Msg("All objectives cleared");
    }

    /// <summary>
    /// Removes the current objective from the list.
    /// Useful when an objective is completed or becomes invalid.
    /// </summary>
    public static void RemoveCurrentObjective()
    {
        if (MONOLITH_OBJECTIVES.Count > 0)
        {
            MONOLITH_OBJECTIVES.RemoveAt(0);
            MelonLogger.Msg("Current objective removed");
        }
    }
}
