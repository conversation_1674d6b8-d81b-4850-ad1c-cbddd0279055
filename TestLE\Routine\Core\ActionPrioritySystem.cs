using System.Collections;
using MelonLoader;

namespace TestLE.Routines.Core;

/// <summary>
/// Manages execution of game actions based on priority and conditions.
/// Implements the Strategy pattern for action selection and Chain of Responsibility
/// for action execution. Replaces the massive switch-like logic in MoveNext().
/// </summary>
public class ActionPrioritySystem
{
    private readonly List<IGameAction> _actions;
    private readonly IGameStateValidator _stateValidator;

    /// <summary>
    /// Initializes the action priority system with registered actions.
    /// Actions are automatically sorted by priority (highest first).
    /// </summary>
    /// <param name="actions">List of available game actions</param>
    /// <param name="stateValidator">Game state validator for pre-execution checks</param>
    public ActionPrioritySystem(IEnumerable<IGameAction> actions, IGameStateValidator stateValidator)
    {
        _stateValidator = stateValidator ?? throw new ArgumentNullException(nameof(stateValidator));
        
        // Sort actions by priority (highest first) for efficient execution
        _actions = actions?.OrderByDescending(a => a.Priority).ToList() 
                   ?? throw new ArgumentNullException(nameof(actions));

        LogRegisteredActions();
    }

    /// <summary>
    /// Finds and executes the highest priority action that can be executed.
    /// Implements fail-fast principle with comprehensive error handling.
    /// </summary>
    /// <returns>Coroutine for the selected action, or null if no action can execute</returns>
    public IEnumerator? ExecuteNextAction()
    {
        // Validate game state before any action execution
        var validationResult = _stateValidator.ValidateGameState();
        if (!validationResult.IsValid)
        {
            MelonLogger.Error($"Game state validation failed: {validationResult.ErrorMessage}");
            return null;
        }

        // Find first action that can execute (highest priority wins)
        foreach (var action in _actions)
        {
            try
            {
                if (action.CanExecute())
                {
                    MelonLogger.Msg($"Executing action: {action.Name} (Priority: {action.Priority})");
                    return action.Execute();
                }
            }
            catch (Exception ex)
            {
                // Log error but continue checking other actions (graceful degradation)
                MelonLogger.Error($"Error checking if action '{action.Name}' can execute: {ex.Message}");
            }
        }

        // No action could be executed
        MelonLogger.Warning("No actions available for execution");
        return null;
    }

    /// <summary>
    /// Gets all registered actions sorted by priority for debugging.
    /// </summary>
    /// <returns>Read-only list of actions</returns>
    public IReadOnlyList<IGameAction> GetRegisteredActions() => _actions.AsReadOnly();

    /// <summary>
    /// Logs all registered actions for debugging and monitoring.
    /// Helps with troubleshooting action priority issues.
    /// </summary>
    private void LogRegisteredActions()
    {
        MelonLogger.Msg($"ActionPrioritySystem initialized with {_actions.Count} actions:");
        foreach (var action in _actions)
        {
            MelonLogger.Msg($"  - {action.Name} (Priority: {action.Priority})");
        }
    }
}
